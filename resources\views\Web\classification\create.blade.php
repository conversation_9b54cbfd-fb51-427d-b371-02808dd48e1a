@extends('Layouts.app')

@section('title', 'SamRx | Add New Classification')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Add New Classification<span class="page-desc text-muted fs-7 fw-semibold pt-1">Create a new classification entry</span></h1>
                            </div>
                            <div>
                                <a href="{{ route('classification.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Classifications</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="classification-create-form" action="{{ route('classification.store') }}" method="POST">
                            @csrf
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_id" class="form-label fw-semibold fs-6 required">Drug Name</label>
                                                <select name="drug_id" id="drug_id" class="form-select form-select-solid @error('drug_id') is-invalid @enderror" required>
                                                    <option value="">Select Drug</option>
                                                    @foreach ($drugs as $drug)
                                                        <option value="{{ $drug->id }}" {{ old('drug_id') == $drug->id ? 'selected' : '' }}>{{ $drug->generic_name }}</option>
                                                    @endforeach
                                                </select>
                                                @error('drug_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Select the drug for this classification</div>
                                            </div>
                                        </div>

                                        <!-- Class Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="class_name" class="form-label fw-semibold fs-6 required">Class Name</label>
                                                <input type="text" name="class_name" id="class_name" class="form-control form-control-solid @error('class_name') is-invalid @enderror" placeholder="Enter class name" value="{{ old('class_name') }}" required>
                                                @error('class_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Name of the classification</div>
                                            </div>
                                        </div>

                                        <!-- Sub Class Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="sub_class_name" class="form-label fw-semibold fs-6 required">Sub Class Name</label>
                                                <input type="text" name="sub_class_name" id="sub_class_name" class="form-control form-control-solid @error('sub_class_name') is-invalid @enderror" placeholder="Enter sub class name" value="{{ old('sub_class_name') }}" required>
                                                @error('sub_class_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Name of the sub classification</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this classification</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Save Classification</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('classification.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'classification-create-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="{{ route("classification.index") }}"]',
                successMessage: 'Classification has been created successfully.',
                redirectUrl: '{{ route("classification.index") }}',
                hasFileUpload: false
            });
        });
    </script>
@endsection
