@extends('Layouts.app')

@section('title', 'RX-Info | Edit Chemical Structure')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Chemical Structure<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update chemical structure information</span></h1>
                            </div>
                            <div>
                                <a href="{{ route('chemical-structure.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Chemical Structure</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="chemical-structure-edit-form" action="{{ route('chemical-structure.update', $chemicalStructure->id) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_id" class="form-label fw-semibold fs-6 required">Drug Name</label>
                                                <select name="drug_id" id="drug_id" class="form-select form-select-solid @error('drug_id') is-invalid @enderror" required>
                                                    <option value="">Select Drug</option>
                                                    @foreach ($drugs as $drug)
                                                        <option value="{{ $drug->id }}" {{ $chemicalStructure->drug_id == $drug->id ? 'selected' : '' }}>{{ $drug->generic_name }}</option>
                                                    @endforeach
                                                </select>
                                                @error('drug_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Select the drug for this chemical structure</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ $chemicalStructure->status == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ $chemicalStructure->status == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this chemical structure</div>
                                            </div>
                                        </div>

                                        <!-- Chemical Structure Image -->
                                        <div class="col-md-12">
                                            <div class="fv-row">
                                                <label for="chemical_structure_image" class="form-label fw-semibold fs-6">Chemical Structure Image</label>
                                                <input type="file" name="chemical_structure_image" id="chemical_structure_image" class="form-control form-control-solid @error('chemical_structure_image') is-invalid @enderror" accept="image/*">
                                                @error('chemical_structure_image')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Upload chemical structure image (JPG, PNG, GIF - Max: 2MB)</div>
                                                <div id="image-preview" class="mt-3" style="display: none;">
                                                    <img id="preview-img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Chemical Structure</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('chemical-structure.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'chemical-structure-edit-form',
                submitBtnId: 'submit-btn',
                imageInputId: 'chemical_structure_image',
                imagePreviewId: 'image-preview',
                previewImageId: 'preview-img',
                exitSelector: 'a[href="{{ route("chemical-structure.index") }}"]',
                successMessage: 'Chemical structure has been updated successfully.',
                redirectUrl: '{{ route("chemical-structure.index") }}',
                hasFileUpload: true
            });
        });
    </script>
@endsection
