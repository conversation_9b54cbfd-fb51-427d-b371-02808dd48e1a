<div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    
    <div class="px-6 app-sidebar-logo" id="kt_app_sidebar_logo">
        <a href="<?php echo e(route('admin-dashboard')); ?>">
            <div class="d-flex align-items-center">
                <img alt="Logo" src="<?php echo e(asset('media/logos/SamRXHard.png')); ?>" class="h-50px app-sidebar-logo-default" />
                <span class="text-white fs-2 font-weight-bold ms-3">SamR<sub>x</sub> | Admin</span>
            </div>
        </a>
        
        <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
            <span class="svg-icon svg-icon-2 rotate-180">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4343 11.2657C11.0468 11.6532 11.0468 12.3468 11.4343 12.7343L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z" fill="currentColor" />
                    <path d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.43431 11.2657C5.04678 11.6532 5.04678 12.3468 5.43431 12.7343L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z" fill="currentColor" />
                </svg>
            </span>
        </div>
    </div>

    
    <div class="overflow-hidden app-sidebar-menu flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="my-5 app-sidebar-wrapper" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
            <div class="menu menu-column menu-rounded menu-sub-indention px-3" id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                
                <div class="menu-item">
                    <a class="menu-link <?php echo e((Request::segment(1) == 'admin-dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('admin-dashboard')); ?>">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="2" width="9" height="9" rx="2" fill="currentColor" />
                                    <rect opacity="0.3" x="13" y="2" width="9" height="9" rx="2" fill="currentColor" />
                                    <rect opacity="0.3" x="13" y="13" width="9" height="9" rx="2" fill="currentColor" />
                                    <rect opacity="0.3" x="2" y="13" width="9" height="9" rx="2" fill="currentColor" />
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Dashboard</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link <?php echo e((Request::segment(1) == 'drugs') ? 'active' : ''); ?>" href="<?php echo e(route('drugs.index')); ?>">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M17.5 11H6.5C4.01 11 2 9.02 2 6.5V6.5C2 3.98 4.01 2 6.5 2H17.5C19.99 2 22 3.98 22 6.5V6.5C22 9.02 19.99 11 17.5 11Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M17.5 22H6.5C4.01 22 2 20.02 2 17.5V17.5C2 14.98 4.01 13 6.5 13H17.5C19.99 13 22 14.98 22 17.5V17.5C22 20.02 19.99 22 17.5 22Z" fill="currentColor"/>
                                    <rect x="4" y="4" width="16" height="3" rx="1" fill="white"/>
                                    <rect x="4" y="15" width="16" height="3" rx="1" fill="white"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Drugs</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link <?php echo e((Request::segment(1) == 'page-under-construction') ? 'active' : ''); ?>" href="<?php echo e(route('page-under-construction')); ?>">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M21 9V7L15 7.5V9.5L21 9ZM3 9V7L9 7.5V9.5L3 9ZM12 10C14.2 10 16 11.8 16 14S14.2 18 12 18S8 16.2 8 14S9.8 10 12 10Z" fill="currentColor"/>
                                    <path d="M12 22C10.9 22 10 21.1 10 20C10 18.9 10.9 18 12 18C13.1 18 14 18.9 14 20C14 21.1 13.1 22 12 22Z" fill="currentColor"/>
                                    <circle cx="6" cy="14" r="2" fill="currentColor"/>
                                    <circle cx="18" cy="14" r="2" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Diseases</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link <?php echo e((Request::segment(1) == 'chemical-structure') ? 'active' : ''); ?>" href="<?php echo e(route('chemical-structure.index')); ?>">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="6" cy="6" r="3" fill="currentColor"/>
                                    <circle cx="18" cy="6" r="3" fill="currentColor"/>
                                    <circle cx="6" cy="18" r="3" fill="currentColor"/>
                                    <circle cx="18" cy="18" r="3" fill="currentColor"/>
                                    <path opacity="0.3" d="M8.5 7.5L15.5 7.5" stroke="currentColor" stroke-width="2"/>
                                    <path opacity="0.3" d="M7.5 8.5L7.5 15.5" stroke="currentColor" stroke-width="2"/>
                                    <path opacity="0.3" d="M16.5 8.5L16.5 15.5" stroke="currentColor" stroke-width="2"/>
                                    <path opacity="0.3" d="M8.5 16.5L15.5 16.5" stroke="currentColor" stroke-width="2"/>
                                    <path opacity="0.3" d="M8.5 8.5L15.5 15.5" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Chemical Structure</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link <?php echo e((Request::segment(1) == 'adverse-effect') ? 'active' : ''); ?>" href="<?php echo e(route('adverse-effect.index')); ?>">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M12 17C14.76 17 17 14.76 17 12C17 9.24 14.76 7 12 7C9.24 7 7 9.24 7 12C7 14.76 9.24 17 12 17Z" fill="currentColor"/>
                                    <path d="M8 21L9.5 19.5L11 21L9.5 22.5L8 21ZM16 3L17.5 1.5L19 3L17.5 4.5L16 3ZM21 8L22.5 9.5L21 11L19.5 9.5L21 8ZM3 16L4.5 17.5L3 19L1.5 17.5L3 16Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Adverse Effects</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link <?php echo e((Request::segment(1) == 'classification') ? 'active' : ''); ?>" href="<?php echo e(route('classification.index')); ?>">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 5V2C8 1.4 8.4 1 9 1H15C15.6 1 16 1.4 16 2V5H21C21.6 5 22 5.4 22 6V8C22 8.6 21.6 9 21 9H3C2.4 9 2 8.6 2 8V6C2 5.4 2.4 5 3 5H8Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M19 10H5V21C5 21.6 5.4 22 6 22H18C18.6 22 19 21.6 19 21V10Z" fill="currentColor"/>
                                    <rect x="7" y="12" width="10" height="2" rx="1" fill="currentColor"/>
                                    <rect x="7" y="16" width="6" height="2" rx="1" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Classification</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link" href="#">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C13.1 2 14 2.9 14 4V8C14 9.1 13.1 10 12 10C10.9 10 10 9.1 10 8V4C10 2.9 10.9 2 12 2Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M12 14C13.1 14 14 14.9 14 16V20C14 21.1 13.1 22 12 22C10.9 22 10 21.1 10 20V16C10 14.9 10.9 14 12 14Z" fill="currentColor"/>
                                    <path d="M20 10C21.1 10 22 10.9 22 12C22 13.1 21.1 14 20 14H16C14.9 14 14 13.1 14 12C14 10.9 14.9 10 16 10H20Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M8 10C9.1 10 10 10.9 10 12C10 13.1 9.1 14 8 14H4C2.9 14 2 13.1 2 12C2 10.9 2.9 10 4 10H8Z" fill="currentColor"/>
                                    <circle cx="12" cy="12" r="2" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Drug Intraction</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link" href="#">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M12 6C15.31 6 18 8.69 18 12C18 15.31 15.31 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6Z" fill="white"/>
                                    <path d="M12 8C13.1 8 14 8.9 14 10V14C14 15.1 13.1 16 12 16C10.9 16 10 15.1 10 14V10C10 8.9 10.9 8 12 8Z" fill="currentColor"/>
                                    <path d="M8 12C8 10.9 8.9 10 10 10H14C15.1 10 16 10.9 16 12C16 13.1 15.1 14 14 14H10C8.9 14 8 13.1 8 12Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Mechanism of Action</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link" href="#">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M6 6C5.4 6 5 6.4 5 7V8C5 8.6 5.4 9 6 9H7C7.6 9 8 8.6 8 8V7C8 6.4 7.6 6 7 6H6ZM6 10C5.4 10 5 10.4 5 11V12C5 12.6 5.4 13 6 13H7C7.6 13 8 12.6 8 12V11C8 10.4 7.6 10 7 10H6ZM6 14C5.4 14 5 14.4 5 15V16C5 16.6 5.4 17 6 17H7C7.6 17 8 16.6 8 16V15C8 14.4 7.6 14 7 14H6ZM6 18C5.4 18 5 18.4 5 19V20C5 20.6 5.4 21 6 21H7C7.6 21 8 20.6 8 20V19C8 18.4 7.6 18 7 18H6Z" fill="currentColor"/>
                                    <rect x="10" y="7" width="9" height="2" rx="1" fill="currentColor"/>
                                    <rect x="10" y="11" width="9" height="2" rx="1" fill="currentColor"/>
                                    <rect x="10" y="15" width="9" height="2" rx="1" fill="currentColor"/>
                                    <rect x="10" y="19" width="9" height="2" rx="1" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Drug Scheduling</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link" href="#">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C10.9 2 10 2.9 10 4V6C10 7.1 10.9 8 12 8C13.1 8 14 7.1 14 6V4C14 2.9 13.1 2 12 2Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M12 10C8.69 10 6 12.69 6 16C6 19.31 8.69 22 12 22C15.31 22 18 19.31 18 16C18 12.69 15.31 10 12 10Z" fill="currentColor"/>
                                    <circle cx="12" cy="16" r="4" fill="white"/>
                                    <circle cx="12" cy="16" r="2" fill="currentColor"/>
                                    <path d="M4 12C2.9 12 2 11.1 2 10C2 8.9 2.9 8 4 8C5.1 8 6 8.9 6 10C6 11.1 5.1 12 4 12Z" fill="currentColor"/>
                                    <path d="M20 12C18.9 12 18 11.1 18 10C18 8.9 18.9 8 20 8C21.1 8 22 8.9 22 10C22 11.1 21.1 12 20 12Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Drug Receptors</span>
                    </a>
                </div>
                

                
                <div class="menu-item">
                    <a class="menu-link" href="#">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 14C16.4183 14 20 10.4183 20 6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6C4 10.4183 7.58172 14 12 14Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z" fill="currentColor"/>
                                    <path d="M15 8C15 9.65685 13.6569 11 12 11C10.3431 11 9 9.65685 9 8C9 6.34315 10.3431 5 12 5C13.6569 5 15 6.34315 15 8Z" fill="white"/>
                                    <path d="M12 2L13.5 6.5L18 8L13.5 9.5L12 14L10.5 9.5L6 8L10.5 6.5L12 2Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Drug Specialist</span>
                    </a>
                </div>
                

                
                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                    <span class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="feather feather-settings">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 1 1-2.83 2.83l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 1 1-4 0v-.09a1.65 1.65 0 0 0-1-1.51 1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 1 1-2.83-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 1 1 0-4h.09a1.65 1.65 0 0 0 1.51-1 1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 1 1 2.83-2.83l.06.06a1.65 1.65 0 0 0 1.82.33h.09a1.65 1.65 0 0 0 1-1.51V3a2 2 0 1 1 4 0v.09a1.65 1.65 0 0 0 1 1.51h.09a1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 1 1 2.83 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82v.09a1.65 1.65 0 0 0 1.51 1H21a2 2 0 1 1 0 4h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Web Setting</span><span class="menu-arrow"></span>
                    </span>
                    <div class="menu-sub menu-sub-accordion">
                        <div class="menu-item">
                            <a class="menu-link" href="#">
                                <span class="menu-bullet">
                                    <span class="bullet bullet-dot"></span>
                                </span>
                                <span class="menu-title">Web Setting</span>
                            </a>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>

    
    <div id="kt_app_sidebar_footer" class="app-sidebar-footer px-6 py-3 d-flex flex-column justify-content-center position-sticky" style="bottom: 0; background: rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); border-top: 1px solid rgba(255, 255, 255, 0.1);">
        <div class="text-center">
            <span class="text-muted fs-7 fw-semibold">Design and Developed By</span>
            <div class="text-white fs-6 fw-bold mt-1">Abhishek William</div>
        </div>
    </div>
    
</div><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Layouts/sidebar.blade.php ENDPATH**/ ?>