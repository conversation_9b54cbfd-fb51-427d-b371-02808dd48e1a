<?php $__env->startSection('title', 'SamRx | Chemical Structure'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div><h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Chemical Structure Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize chemical structure information</span></h1></div>
                            <div><a href="<?php echo e(route('chemical-structure.create')); ?>" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Chemical Structure</a></div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="chemical-structure-table">
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">#</th>
                                                <th class="min-w-150px">Drug Name</th>
                                                <th class="min-w-100px">Status</th>
                                                <th class="min-w-150px">Created By</th>
                                                <th class="min-w-125px">Created At</th>
                                                <th class="text-center min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Define columns for chemical structure table
            const columns = [
                getColumnDefinition('index'),
                { data: 'drug_id', name: 'drug_id' },
                { data: 'status', name: 'status', orderable: false },
                { data: 'created_by', name: 'created_by' },
                getColumnDefinition('date', { data: 'created_at', name: 'created_at' }),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'chemical-structure-table',
                ajaxUrl: '<?php echo e(route("chemical-structure.index")); ?>',
                columns: columns,
                itemName: 'chemical structure',
                order: [[4, 'desc']], // Order by created_at desc
                responsive: false, // Disable responsive feature to show all columns
                scrollX: true, // Enable horizontal scrolling
                language: {
                    emptyTable: '<div class="text-center py-4"><i class="fas fa-pills fa-3x text-muted mb-3"></i><br><span class="text-muted">No chemical structures found</span></div>'
                }
            });

            // Delete functionality using universal function
            $(document).on('click', '.delete-chemical-structure', function() {
                const chemicalStructureId = $(this).data('id');
                universalDelete({id: chemicalStructureId, url: '<?php echo e(route("chemical-structure.destroy", ":id")); ?>', itemName: 'chemical structure', table: table});
            });

            // Refresh button functionality using helper function
            $(document).on('click', '.refresh-table', function() {
                refreshTable(table, 'Chemical structure table has been refreshed.');
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Web/chemical-structure/index.blade.php ENDPATH**/ ?>