

<?php $__env->startSection('title', 'SamRx | Edit Classification'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Classification<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update classification information</span></h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('classification.index')); ?>" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Classifications</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="classification-edit-form" action="<?php echo e(route('classification.update', $classification->id)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_id" class="form-label fw-semibold fs-6 required">Drug Name</label>
                                                <select name="drug_id" id="drug_id" class="form-select form-select-solid <?php $__errorArgs = ['drug_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                                    <option value="">Select Drug</option>
                                                    <?php $__currentLoopData = $drugs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $drug): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($drug->id); ?>" <?php echo e($classification->drug_id == $drug->id ? 'selected' : ''); ?>><?php echo e($drug->generic_name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <?php $__errorArgs = ['drug_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Select the drug for this classification</div>
                                            </div>
                                        </div>

                                        <!-- Class Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="class_name" class="form-label fw-semibold fs-6 required">Class Name</label>
                                                <input type="text" name="class_name" id="class_name" class="form-control form-control-solid <?php $__errorArgs = ['class_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter class name" value="<?php echo e($classification->class_name); ?>" required>
                                                <?php $__errorArgs = ['class_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Name of the classification</div>
                                            </div>
                                        </div>

                                        <!-- Sub Class Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="sub_class_name" class="form-label fw-semibold fs-6 required">Sub Class Name</label>
                                                <input type="text" name="sub_class_name" id="sub_class_name" class="form-control form-control-solid <?php $__errorArgs = ['sub_class_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter sub class name" value="<?php echo e($classification->sub_class_name); ?>" required>
                                                <?php $__errorArgs = ['sub_class_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Name of the sub classification</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" <?php echo e($classification->status == '1' ? 'selected' : ''); ?>>Active</option>
                                                    <option value="0" <?php echo e($classification->status == '0' ? 'selected' : ''); ?>>Inactive</option>
                                                </select>
                                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Set the status for this classification</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Classification</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="<?php echo e(route('classification.index')); ?>" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'classification-edit-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="<?php echo e(route("classification.index")); ?>"]',
                successMessage: 'Classification has been updated successfully.',
                redirectUrl: '<?php echo e(route("classification.index")); ?>',
                hasFileUpload: false
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Web/classification/edit.blade.php ENDPATH**/ ?>