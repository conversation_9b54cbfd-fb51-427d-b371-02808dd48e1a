<?php

use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Web\{AdverseEffectController, ChecmicalStructureController, ClassificationController, DrugsController};
use Illuminate\Support\Facades\Route;

// Login Routes
Route::get('/', [AuthController::class, 'showLoginForm'])->name('showloginform');
Route::post('/login', [AuthController::class, 'login'])->name('login');

Route::group(['middleware' => ['admin.auth']], function () {
    // Page Under Construction Routes
    Route::get('/page-under-construction', function () {
        return view('Layouts.message');
    })->name('page-under-construction');

    // Dashboard and Logout Routes
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/admin-dashboard', [AuthController::class, 'showDashboard'])->name('admin-dashboard');

    // Drugs Routes
    Route::resource('drugs', DrugsController::class);
    // Chemical Structure Routes
    Route::resource('chemical-structure', ChecmicalStructureController::class);
    // Adverse Effects Routes
    Route::resource('adverse-effect', AdverseEffectController::class);
    // Classification Routes
    Route::resource('classification', ClassificationController::class);
});
